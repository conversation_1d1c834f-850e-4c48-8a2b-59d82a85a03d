import { DurableObject } from 'cloudflare:workers';
import type { D1Database, DurableObjectState } from '@cloudflare/workers-types';
import { incomeDataFetcher, BatchProcessor, scheduledBatchUpdateAllData } from './dataFetcher-new';
import type { Env } from './types';

// 定时任务状态
export interface ScheduledTaskState {
  taskId: string;
  status: 'waiting' | 'checking' | 'retrying' | 'processing' | 'completed' | 'failed';
  startTime: number;
  lastCheckTime: number;
  retryCount: number;
  maxRetries: number;
  retryInterval: number; // 30分钟 = 30 * 60 * 1000 毫秒
  firstAccountPhone?: string;
  firstAccountSessionId?: string;
  errorMessage?: string;
}

/**
 * 定时任务 Durable Object
 * 处理每天10点的定时任务逻辑：
 * 1. 激活DO
 * 2. 获取第一个账号的收益数据
 * 3. 检查is_yesterday_income_ready
 * 4. 如果为false，设置30分钟ALARM重试
 * 5. 直到为true，开始分批执行所有账号数据获取
 */
export class ScheduledTaskDO extends DurableObject {
  private state: DurableObjectState;
  private db: D1Database;
  private env: Env;
  private currentTask: ScheduledTaskState | null = null;

  constructor(state: DurableObjectState, env: Env) {
    super(state, env);
    this.state = state;
    this.db = env.DB;
    this.env = env;

    // 初始化时检查是否有未完成的任务
    this.state.blockConcurrencyWhile(async () => {
      const storedTask = await this.state.storage.get<ScheduledTaskState>('currentTask');
      if (storedTask && ['checking', 'retrying'].includes(storedTask.status)) {
        this.currentTask = storedTask;
        console.log(`恢复未完成的定时任务: ${storedTask.taskId}`);
      }
    });
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const path = url.pathname;

    try {
      if (path === '/start-scheduled-task' && request.method === 'POST') {
        return await this.startScheduledTask();
      } else if (path === '/get-task-status' && request.method === 'GET') {
        return await this.getTaskStatus();
      } else if (path === '/cancel-task' && request.method === 'POST') {
        return await this.cancelTask();
      }

      return new Response('Not Found', { status: 404 });
    } catch (error) {
      console.error('ScheduledTaskDO fetch error:', error);
      return new Response(JSON.stringify({
        success: false,
        message: `处理请求失败: ${error}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * 启动定时任务
   */
  private async startScheduledTask(): Promise<Response> {
    try {
      // 检查是否已有正在进行的任务
      if (this.currentTask && ['checking', 'retrying', 'processing'].includes(this.currentTask.status)) {
        return new Response(JSON.stringify({
          success: false,
          message: `已有正在进行的任务: ${this.currentTask.taskId}`,
          taskId: this.currentTask.taskId,
          status: this.currentTask.status
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 获取第一个账号信息
      const processor = new BatchProcessor(this.db);
      const accounts = await processor.getAllAccounts();

      if (accounts.length === 0) {
        return new Response(JSON.stringify({
          success: false,
          message: '没有找到任何平台账号'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      const firstAccount = accounts[0];
      const taskId = `scheduled_task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // 创建新任务状态
      this.currentTask = {
        taskId,
        status: 'checking',
        startTime: Date.now(),
        lastCheckTime: Date.now(),
        retryCount: 0,
        maxRetries: 10, // 最多重试10次（5小时）
        retryInterval: 30 * 60 * 1000, // 30分钟
        firstAccountPhone: firstAccount.phone,
        firstAccountSessionId: firstAccount.sessionid
      };

      // 保存状态
      await this.state.storage.put('currentTask', this.currentTask);

      // 立即开始第一次检查
      await this.state.storage.setAlarm(Date.now() + 100);

      console.log(`🚀 [ScheduledTaskDO] 定时任务已启动: ${taskId}, 第一个账号: ${firstAccount.phone}`);

      return new Response(JSON.stringify({
        success: true,
        message: '定时任务已启动',
        taskId,
        firstAccountPhone: firstAccount.phone
      }), {
        headers: { 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('启动定时任务失败:', error);
      return new Response(JSON.stringify({
        success: false,
        message: `启动失败: ${error}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * 获取任务状态
   */
  private async getTaskStatus(): Promise<Response> {
    return new Response(JSON.stringify({
      success: true,
      task: this.currentTask
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * 取消任务
   */
  private async cancelTask(): Promise<Response> {
    if (this.currentTask) {
      this.currentTask.status = 'failed';
      this.currentTask.errorMessage = '任务被手动取消';
      await this.state.storage.put('currentTask', this.currentTask);
      
      // 清除alarm
      await this.state.storage.deleteAlarm();
    }

    return new Response(JSON.stringify({
      success: true,
      message: '任务已取消'
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * Alarm 处理器 - 检查第一个账号的收益数据
   */
  async alarm(): Promise<void> {
    console.log('🔔 [ScheduledTaskDO] Alarm 被触发！');

    if (!this.currentTask || !['checking', 'retrying'].includes(this.currentTask.status)) {
      console.log('❌ [ScheduledTaskDO] 没有需要处理的任务或状态不正确');
      return;
    }

    try {
      console.log(`🔍 [ScheduledTaskDO] 开始检查第一个账号收益数据: ${this.currentTask.firstAccountPhone}`);
      
      // 获取第一个账号的收益数据
      const incomeResult = await incomeDataFetcher(
        this.currentTask.firstAccountPhone!,
        this.currentTask.firstAccountSessionId!
      );

      this.currentTask.lastCheckTime = Date.now();

      if (incomeResult.success && incomeResult.isYesterdayIncomeReady) {
        // 收益数据已准备好，开始批量处理
        console.log(`✅ [ScheduledTaskDO] 第一个账号收益数据已准备好，开始批量处理所有账号`);
        
        this.currentTask.status = 'processing';
        await this.state.storage.put('currentTask', this.currentTask);

        // 启动批量处理
        const batchResult = await scheduledBatchUpdateAllData(this.db, undefined, this.env);
        
        if (batchResult.success) {
          this.currentTask.status = 'completed';
          console.log(`🎉 [ScheduledTaskDO] 定时任务完成！批量处理结果: ${batchResult.message}`);
        } else {
          this.currentTask.status = 'failed';
          this.currentTask.errorMessage = `批量处理失败: ${batchResult.message}`;
          console.error(`❌ [ScheduledTaskDO] 批量处理失败: ${batchResult.message}`);
        }

        await this.state.storage.put('currentTask', this.currentTask);

      } else {
        // 收益数据还未准备好，需要重试
        this.currentTask.retryCount++;
        
        if (this.currentTask.retryCount >= this.currentTask.maxRetries) {
          // 达到最大重试次数
          this.currentTask.status = 'failed';
          this.currentTask.errorMessage = `达到最大重试次数 (${this.currentTask.maxRetries})，收益数据仍未准备好`;
          await this.state.storage.put('currentTask', this.currentTask);
          
          console.error(`❌ [ScheduledTaskDO] 达到最大重试次数，任务失败`);
          return;
        }

        // 设置下次重试
        this.currentTask.status = 'retrying';
        await this.state.storage.put('currentTask', this.currentTask);
        
        // 设置30分钟后的alarm
        await this.state.storage.setAlarm(Date.now() + this.currentTask.retryInterval);
        
        console.log(`⏰ [ScheduledTaskDO] 收益数据未准备好 (is_yesterday_income_ready: ${incomeResult.isYesterdayIncomeReady}), 30分钟后重试 (${this.currentTask.retryCount}/${this.currentTask.maxRetries})`);
      }

    } catch (error) {
      console.error('定时任务处理失败:', error);
      
      this.currentTask.status = 'failed';
      this.currentTask.errorMessage = `处理异常: ${error}`;
      await this.state.storage.put('currentTask', this.currentTask);
    }
  }
}
