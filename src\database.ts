import type { D1Database } from '@cloudflare/workers-types';
import {
  User,
  ActivationCodeType,
  ActivationCode,
  ActivationRecord,
  PlatformAccountData,
  PlatformAccountStats,
  MainAccountPlatformData,
  PlatformAccountsCollection
} from './types';

// 数据验证函数
export function validatePlatformAccountData(data: any): data is PlatformAccountData {
  console.log(`  验证平台账号数据:`, data);

  if (!data || typeof data !== 'object') {
    console.log(`  验证失败: 数据不是对象`);
    return false;
  }

  // 检查必需字段
  const requiredFields = ['phone', 'login_type', 'username', 'account_status'];
  for (const field of requiredFields) {
    if (!data[field] || typeof data[field] !== 'string') {
      console.log(`  验证失败: 缺少必需字段 ${field} 或类型错误，值:`, data[field]);
      return false;
    }
  }

  // 检查stats对象
  if (!data.stats || typeof data.stats !== 'object') {
    console.log(`  验证失败: stats字段缺失或类型错误，值:`, data.stats);
    return false;
  }

  // 检查数字字段
  const numericFields = ['owner_id', 'current_holder_id'];
  for (const field of numericFields) {
    if (data[field] !== undefined && typeof data[field] !== 'number') {
      console.log(`  验证失败: 数字字段 ${field} 类型错误，值:`, data[field]);
      return false;
    }
  }

  console.log(`  验证成功: 账号 ${data.phone}`);
  return true;
}

/**
 * 创建标准的平台账号数据结构
 * 确保所有新添加的账号都使用统一的数据结构
 * 注意：新账号的统计数据初始化为'0'，需要通过数据更新API获取真实数据
 */
export function createStandardPlatformAccountData(
  phone: string,
  platform: string = '头条号',
  loginType: string = '文章',
  teamTag: string = '',
  username: string = '',
  sessionid: string = '',
  homepageUrl: string = '',
  isVerified: string = '否',
  draftsCount: string = '-',
  accountStatus: string = '正常',
  ownerId: number,
  currentHolderId: number
): PlatformAccountData {
  const now = new Date().toISOString();

  return {
    phone,
    platform,
    login_type: loginType,
    team_tag: teamTag,
    data_update_time: now,
    login_time: now,
    username,
    sessionid,
    homepage_url: homepageUrl,
    is_verified: isVerified,
    drafts_count: draftsCount,
    stats: {
      followers: '0',
      total_reads: '0',
      total_income: '0',
      yesterday_reads: '0',
      yesterday_income: '0',
      credit_score: '0',
      can_withdraw_amount: '0'
    },
    account_status: accountStatus,
    is_yesterday_income_ready: false, // 新账号默认为 false
    owner_id: ownerId,
    current_holder_id: currentHolderId,
    created_at: now,
    updated_at: now
  };
}

/**
 * 创建带有真实数据的平台账号数据结构
 * 如果提供了sessionid，会尝试获取真实的统计数据
 */
export async function createPlatformAccountDataWithRealStats(
  phone: string,
  platform: string = '头条号',
  loginType: string = '文章',
  teamTag: string = '',
  username: string = '',
  sessionid: string = '',
  homepageUrl: string = '',
  isVerified: string = '否',
  draftsCount: string = '-',
  accountStatus: string = '正常',
  ownerId: number,
  currentHolderId: number
): Promise<PlatformAccountData> {
  // 先创建基础数据结构
  const baseData = createStandardPlatformAccountData(
    phone, platform, loginType, teamTag, username, sessionid,
    homepageUrl, isVerified, draftsCount, accountStatus, ownerId, currentHolderId
  );

  // 如果有sessionid，尝试获取真实数据
  if (sessionid && sessionid.trim() !== '') {
    try {
      console.log(`尝试为新账号 ${phone} 获取真实统计数据...`);

      // 动态导入数据获取器以避免循环依赖
      const { allDataFetcher } = await import('./dataFetcher-new');
      const result = await allDataFetcher(phone, sessionid);

      if (result.success && result.data?.stats) {
        console.log(`成功获取账号 ${phone} 的真实数据`);
        // 合并真实数据到基础数据结构
        baseData.stats = { ...baseData.stats, ...result.data.stats };
        if (result.data.username) baseData.username = result.data.username;
        if (result.data.is_verified) baseData.is_verified = result.data.is_verified;
        if (result.data.account_status) baseData.account_status = result.data.account_status;
        baseData.data_update_time = new Date().toISOString();
      } else {
        console.log(`获取账号 ${phone} 真实数据失败，使用默认值: ${result.message}`);
      }
    } catch (error) {
      console.error(`获取账号 ${phone} 真实数据异常:`, error);
      // 发生错误时仍然返回基础数据结构
    }
  }

  return baseData;
}

// JSON数据验证函数
export function validatePlatformAccountsCollection(jsonStr: string): PlatformAccountsCollection | null {
  try {
    console.log('开始验证平台账号集合数据...');
    console.log(`JSON字符串长度: ${jsonStr.length}`);

    const data = JSON.parse(jsonStr);
    console.log('JSON解析成功，数据结构:', Object.keys(data));

    if (!data || typeof data !== 'object') {
      console.error('数据不是有效对象');
      return null;
    }

    if (!data.accounts || typeof data.accounts !== 'object') {
      console.error('缺少accounts字段或类型错误');
      console.log('实际的accounts字段:', data.accounts);
      return null;
    }

    if (!data.metadata || typeof data.metadata !== 'object') {
      console.error('缺少metadata字段或类型错误');
      console.log('实际的metadata字段:', data.metadata);
      return null;
    }

    const accountsCount = Object.keys(data.accounts).length;
    console.log(`找到 ${accountsCount} 个平台账号`);

    // 验证每个平台账号数据
    for (const [phone, accountData] of Object.entries(data.accounts)) {
      console.log(`验证账号: ${phone}`);
      if (!validatePlatformAccountData(accountData)) {
        console.error(`无效的平台账号数据: ${phone}`, accountData);
        return null;
      }
    }

    console.log('平台账号集合数据验证通过');
    return data as PlatformAccountsCollection;
  } catch (error) {
    console.error('JSON解析失败:', error);
    console.error('原始JSON字符串:', jsonStr);
    return null;
  }
}



// 初始化数据库表
export async function initializeDB(db: D1Database): Promise<void> {
  try {
    
    // 创建用户表
    await db.exec(`CREATE TABLE IF NOT EXISTS users (id INTEGER PRIMARY KEY AUTOINCREMENT, phone TEXT UNIQUE NOT NULL, password TEXT NOT NULL, created_at TEXT NOT NULL, last_login_at TEXT, expiry_date TEXT, account_type TEXT DEFAULT '主账号', account_owner INTEGER, FOREIGN KEY (account_owner) REFERENCES users(id) ON DELETE SET NULL)`);
    
    // 创建激活码表
    await db.exec(`CREATE TABLE IF NOT EXISTS activation_codes (id INTEGER PRIMARY KEY AUTOINCREMENT, code TEXT UNIQUE NOT NULL, type INTEGER NOT NULL, created_at TEXT NOT NULL, used_at TEXT, used_by INTEGER, FOREIGN KEY (used_by) REFERENCES users(id) ON DELETE SET NULL)`);
    
    // 创建激活记录表
    await db.exec(`CREATE TABLE IF NOT EXISTS activation_records (id INTEGER PRIMARY KEY AUTOINCREMENT, code TEXT NOT NULL, user_id INTEGER NOT NULL, used_at TEXT NOT NULL, phone TEXT NOT NULL, days INTEGER NOT NULL, FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE)`);

    // 创建主账号平台数据表
    await db.exec(`CREATE TABLE IF NOT EXISTS main_account_platform_data (main_account_id INTEGER PRIMARY KEY, platform_accounts_data TEXT NOT NULL, updated_at TEXT NOT NULL, FOREIGN KEY (main_account_id) REFERENCES users(id))`);
    console.log("主账号平台数据表创建成功");

    console.log("数据库表初始化成功");
  } catch (error) {
    console.error("初始化数据库表失败:", error);
    throw error;
  }
}

// 用户注册
export async function registerUser(db: D1Database, phone: string, password: string, accountType: string = '主账号', accountOwner: number | null = null): Promise<{ success: boolean; message: string; userId?: number }> {
  try {
    // 检查手机号是否已被注册
    const existingUser = await db.prepare(`SELECT id FROM users WHERE phone = ?`).bind(phone).first();
    if (existingUser) {
      return { success: false, message: "手机号已注册" };
    }

    // 创建新用户
    const now = new Date().toISOString();
    const result = await db.prepare(`INSERT INTO users (phone, password, created_at, account_type, account_owner) VALUES (?, ?, ?, ?, ?)`).bind(phone, password, now, accountType, accountOwner).run();

    return {
      success: true,
      message: "注册成功",
      userId: result.meta.last_row_id as number
    };
  } catch (error) {
    return { success: false, message: `注册失败: ${error}` };
  }
}

// 用户登录验证
export async function validateLogin(db: D1Database, phone: string, password: string): Promise<{ success: boolean; message: string; user?: User }> {
  try {
    const user = await db.prepare(`SELECT * FROM users WHERE phone = ? AND password = ?`).bind(phone, password).first<User>();

    if (!user) {
      return { success: false, message: "手机号或密码不正确" };
    }

    // 更新上次登录时间
    const now = new Date().toISOString();
    await db.prepare(`UPDATE users SET last_login_at = ? WHERE id = ?`).bind(now, user.id).run();

    // 更新用户的登录时间
    user.last_login_at = now;

    return { success: true, message: "登录成功", user };
  } catch (error) {
    return { success: false, message: `登录失败: ${error}` };
  }
}

// 获取用户信息
export async function getUserByPhone(db: D1Database, phone: string): Promise<User | null> {
  return db.prepare(`SELECT * FROM users WHERE phone = ?`).bind(phone).first<User>();
}

// 获取用户信息通过ID
export async function getUserById(db: D1Database, id: number): Promise<User | null> {
  return db.prepare(`SELECT * FROM users WHERE id = ?`).bind(id).first<User>();
}

// 完整用户信息接口
export interface CompleteUserInfo {
  id: number;
  phone: string;
  account_type: string;
  account_owner: number | null;
  created_at: string;
  last_login_at: string | null;
  expiry_date: string | null;
  owner_phone?: string;
  sub_accounts?: User[];
}

// 获取完整用户信息（包括子账号列表）
export async function getUserCompleteInfo(db: D1Database, userId: number): Promise<{ success: boolean; userInfo?: CompleteUserInfo; message?: string }> {
  try {
    // 获取用户基本信息和主账号信息
    const user = await db.prepare(`
      SELECT
        users.id,
        users.phone,
        users.account_type,
        users.account_owner,
        users.created_at,
        users.last_login_at,
        users.expiry_date,
        owner.phone as owner_phone
      FROM users
      LEFT JOIN users as owner ON users.account_owner = owner.id
      WHERE users.id = ?
    `).bind(userId).first<User & { owner_phone?: string }>();

    if (!user) {
      return { success: false, message: "用户不存在" };
    }

    const userInfo: CompleteUserInfo = {
      id: user.id,
      phone: user.phone,
      account_type: user.account_type,
      account_owner: user.account_owner,
      created_at: user.created_at,
      last_login_at: user.last_login_at,
      expiry_date: user.expiry_date,
      owner_phone: user.owner_phone
    };

    // 如果是主账号，获取子账号列表
    if (user.account_type === '主账号') {
      const subAccountsResult = await getSubAccounts(db, user.id);
      if (subAccountsResult.success && subAccountsResult.subAccounts) {
        userInfo.sub_accounts = subAccountsResult.subAccounts;
      }
    }

    return {
      success: true,
      userInfo
    };
  } catch (error) {
    console.error('获取完整用户信息失败:', error);
    return { success: false, message: `获取完整用户信息失败: ${error}` };
  }
}

// 生成激活码
export async function generateActivationCode(db: D1Database, type: ActivationCodeType, count: number = 1): Promise<{ success: boolean; message: string; codes?: string[] }> {
  try {
    const now = new Date().toISOString();
    const codes: string[] = [];
    
    // 开始事务
    const statements = [];
    
    for (let i = 0; i < count; i++) {
      // 生成16位随机字符串作为激活码
      const code = Array(16).fill(0).map(() => {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        return chars.charAt(Math.floor(Math.random() * chars.length));
      }).join('');
      
      codes.push(code);
      
      // 准备SQL语句
      statements.push(
        db.prepare(`INSERT INTO activation_codes (code, type, created_at) VALUES (?, ?, ?)`)
          .bind(code, type, now)
      );
    }
    
    // 执行批量插入
    await db.batch(statements);
    
    return { 
      success: true, 
      message: `成功生成${count}个${type}天的激活码`, 
      codes 
    };
  } catch (error) {
    return { success: false, message: `生成激活码失败: ${error}` };
  }
}

// 使用激活码
export async function useActivationCode(db: D1Database, code: string, userId: number, phone: string): Promise<{ success: boolean; message: string; days?: number }> {
  try {
    // 查找激活码
    const activationCode = await db.prepare(`SELECT * FROM activation_codes WHERE code = ?`).bind(code).first<ActivationCode>();
    
    if (!activationCode) {
      return { success: false, message: "激活码不存在" };
    }
    
    if (activationCode.used_at) {
      return { success: false, message: "激活码已被使用" };
    }
    
    // 如果是1天的激活码，检查用户是否已经使用过1天激活码
    if (activationCode.type === ActivationCodeType.DAY_1) {
      const usedOneDayCode = await db.prepare(`
        SELECT COUNT(*) as count 
        FROM activation_records 
        WHERE user_id = ? AND days = 1
      `).bind(userId).first<{ count: number }>();
      
      if (usedOneDayCode && usedOneDayCode.count > 0) {
        return { success: false, message: "每个账号只能使用一次1天激活码" };
      }
    }
    
    const now = new Date().toISOString();
    
    // 更新激活码状态
    await db.prepare(`
      UPDATE activation_codes 
      SET used_at = ?, used_by = ? 
      WHERE id = ?
    `).bind(now, userId, activationCode.id).run();
    
    // 创建激活记录
    await db.prepare(`
      INSERT INTO activation_records (code, user_id, used_at, phone, days) 
      VALUES (?, ?, ?, ?, ?)
    `).bind(code, userId, now, phone, activationCode.type).run();
    
    // 更新用户过期时间
    const user = await db.prepare(`SELECT expiry_date FROM users WHERE id = ?`).bind(userId).first<{ expiry_date: string | null }>();
    
    let expiryDate = user?.expiry_date ? new Date(user.expiry_date) : new Date();
    
    // 如果过期日期已经是过去，则从现在开始计算
    if (expiryDate < new Date()) {
      expiryDate = new Date();
    }
    
    // 添加天数
    expiryDate.setDate(expiryDate.getDate() + activationCode.type);
    
    // 更新用户过期时间
    await db.prepare(`UPDATE users SET expiry_date = ? WHERE id = ?`).bind(expiryDate.toISOString(), userId).run();
    
    return { 
      success: true, 
      message: `成功激活${activationCode.type}天会员`, 
      days: activationCode.type
    };
  } catch (error) {
    return { success: false, message: `使用激活码失败: ${error}` };
  }
}

// 获取激活码列表
export async function getActivationCodes(db: D1Database, type?: ActivationCodeType, used?: boolean): Promise<{ success: boolean; codes?: ActivationCode[]; message?: string }> {
  try {
    // 首先检查activation_codes表是否存在
    const tableCheck = await db.prepare(`SELECT name FROM sqlite_master WHERE type='table' AND name='activation_codes'`).first<{ name: string }>();
    
    if (!tableCheck) {
      return { 
        success: true, 
        codes: [],
        message: "激活码表不存在，返回空列表" 
      };
    }
    
    let query = `SELECT * FROM activation_codes`;
    const params: any[] = [];
    
    const conditions: string[] = [];
    
    if (type !== undefined) {
      conditions.push(`type = ?`);
      params.push(type);
    }
    
    if (used !== undefined) {
      if (used) {
        conditions.push(`used_at IS NOT NULL`);
      } else {
        conditions.push(`used_at IS NULL`);
      }
    }
    
    if (conditions.length > 0) {
      query += ` WHERE ` + conditions.join(' AND ');
    }
    
    query += ` ORDER BY created_at DESC`;
    
    const result = await db.prepare(query).bind(...params).all<ActivationCode>();
    
    return { 
      success: true, 
      codes: result.results 
    };
  } catch (error) {
    console.error("获取激活码列表失败:", error);
    return { success: false, message: `获取激活码列表失败: ${error}` };
  }
}

// 获取激活记录
export async function getActivationRecords(db: D1Database): Promise<{ success: boolean; records?: any[]; message?: string }> {
  try {
    const query = `
      SELECT
        ar.id,
        ar.code,
        ar.used_at,
        ar.phone,
        ar.days,
        u.phone as user_phone
      FROM activation_records ar
      JOIN users u ON ar.user_id = u.id
      ORDER BY ar.used_at DESC
    `;

    const result = await db.prepare(query).all();

    return {
      success: true,
      records: result.results
    };
  } catch (error) {
    console.error("获取激活记录失败:", error);
    return { success: false, message: `获取激活记录失败: ${error}` };
  }
}

// 新的平台账号数据管理函数

// 获取主账号ID（如果是子账号，返回其主账号ID）
async function getMainAccountId(db: D1Database, userId: number): Promise<number> {
  const userInfo = await db.prepare(`
    SELECT account_owner FROM users WHERE id = ?
  `).bind(userId).first<{ account_owner: number | null }>();

  return userInfo?.account_owner || userId;
}

// 新的平台账号数据管理API

// 批量更新平台账号数据
export async function batchUpdatePlatformAccounts(
  db: D1Database,
  userId: number,
  accountsData: { [phone: string]: PlatformAccountData }
): Promise<{ success: boolean; message: string }> {
  try {
    // 验证输入数据
    for (const [phone, accountData] of Object.entries(accountsData)) {
      if (!validatePlatformAccountData(accountData)) {
        return { success: false, message: `无效的平台账号数据: ${phone}` };
      }
    }

    const mainAccountId = await getMainAccountId(db, userId);
    const now = new Date().toISOString();

    // 获取现有数据
    const existing = await db.prepare(`
      SELECT platform_accounts_data FROM main_account_platform_data WHERE main_account_id = ?
    `).bind(mainAccountId).first<{ platform_accounts_data: string }>();

    let existingPlatformData: PlatformAccountsCollection;

    if (existing) {
      // 解析现有数据
      const parsedData = validatePlatformAccountsCollection(existing.platform_accounts_data);
      if (parsedData) {
        existingPlatformData = parsedData;
      } else {
        // 如果现有数据无效，创建新的空数据结构
        existingPlatformData = {
          accounts: {},
          metadata: {
            total_accounts: 0,
            last_batch_update: now
          }
        };
      }
    } else {
      // 创建新的数据结构
      existingPlatformData = {
        accounts: {},
        metadata: {
          total_accounts: 0,
          last_batch_update: now
        }
      };
    }

    // 合并新数据到现有数据中
    for (const [phone, accountData] of Object.entries(accountsData)) {
      existingPlatformData.accounts[phone] = {
        ...accountData,
        updated_at: now
      };
    }

    // 更新元数据
    existingPlatformData.metadata.total_accounts = Object.keys(existingPlatformData.accounts).length;
    existingPlatformData.metadata.last_batch_update = now;

    if (existing) {
      // 更新现有记录
      await db.prepare(`
        UPDATE main_account_platform_data
        SET platform_accounts_data = ?, updated_at = ?
        WHERE main_account_id = ?
      `).bind(JSON.stringify(existingPlatformData), now, mainAccountId).run();
    } else {
      // 插入新记录
      await db.prepare(`
        INSERT INTO main_account_platform_data (main_account_id, platform_accounts_data, updated_at)
        VALUES (?, ?, ?)
      `).bind(mainAccountId, JSON.stringify(existingPlatformData), now).run();
    }

    return {
      success: true,
      message: `成功更新 ${Object.keys(accountsData).length} 个平台账号数据`
    };
  } catch (error) {
    console.error("批量更新平台账号失败:", error);
    return { success: false, message: `批量更新失败: ${error}` };
  }
}

// 获取平台账号数据
export async function getPlatformAccountsData(
  db: D1Database,
  userId: number,
  phones?: string[]
): Promise<{ success: boolean; accounts?: { [phone: string]: PlatformAccountData }; message?: string }> {
  try {
    const mainAccountId = await getMainAccountId(db, userId);

    // 获取该主账号的所有平台账号数据
    const result = await db.prepare(`
      SELECT platform_accounts_data FROM main_account_platform_data WHERE main_account_id = ?
    `).bind(mainAccountId).first<{ platform_accounts_data: string }>();

    if (!result) {
      return {
        success: true,
        accounts: {}
      };
    }

    // 使用新的验证函数解析和验证JSON数据
    const platformData = validatePlatformAccountsCollection(result.platform_accounts_data);
    if (!platformData) {
      console.error("平台账号数据验证失败");
      return { success: false, message: "数据格式错误或无效" };
    }

    // 根据用户权限筛选数据
    const userInfo = await db.prepare(`
      SELECT account_type FROM users WHERE id = ?
    `).bind(userId).first<{ account_type: string }>();

    const isMainAccount = userInfo?.account_type === '主账号';
    let filteredAccounts: { [phone: string]: PlatformAccountData } = {};

    for (const [phone, accountData] of Object.entries(platformData.accounts)) {
      // 主账号可以看到所有账号，子账号只能看到归属于自己的账号
      if (isMainAccount || accountData.current_holder_id === userId) {
        // 如果指定了特定手机号，只返回匹配的
        if (!phones || phones.includes(phone)) {
          filteredAccounts[phone] = accountData;
        }
      }
    }

    return {
      success: true,
      accounts: filteredAccounts
    };
  } catch (error) {
    console.error("获取平台账号数据失败:", error);
    return { success: false, message: `获取数据失败: ${error}` };
  }
}



// 单个平台账号更新
export async function updateSinglePlatformAccount(
  db: D1Database,
  userId: number,
  phone: string,
  updateData: Partial<PlatformAccountData>
): Promise<{ success: boolean; message: string }> {
  try {
    // 验证更新数据
    if (updateData.phone && updateData.phone !== phone) {
      return { success: false, message: "不允许修改平台账号手机号" };
    }

    const mainAccountId = await getMainAccountId(db, userId);

    // 获取现有数据
    const result = await db.prepare(`
      SELECT platform_accounts_data FROM main_account_platform_data WHERE main_account_id = ?
    `).bind(mainAccountId).first<{ platform_accounts_data: string }>();

    if (!result) {
      return { success: false, message: "未找到平台账号数据" };
    }

    // 使用验证函数解析JSON数据
    const platformData = validatePlatformAccountsCollection(result.platform_accounts_data);
    if (!platformData) {
      return { success: false, message: "数据格式错误或无效" };
    }

    // 检查账号是否存在
    if (!platformData.accounts[phone]) {
      return { success: false, message: "平台账号不存在" };
    }

    // 检查权限
    const userInfo = await db.prepare(`
      SELECT account_type FROM users WHERE id = ?
    `).bind(userId).first<{ account_type: string }>();

    const isMainAccount = userInfo?.account_type === '主账号';
    const currentAccount = platformData.accounts[phone];

    if (!isMainAccount && currentAccount.current_holder_id !== userId) {
      return { success: false, message: "没有权限操作此平台账号" };
    }

    // 记录更新前的数据
    console.log(`📝 更新账号 ${phone} 数据前:`);
    console.log(`  当前数据:`, JSON.stringify(currentAccount, null, 2));
    console.log(`  更新数据:`, JSON.stringify(updateData, null, 2));

    // 合并更新数据 - 保留现有字段，只更新传入的字段
    platformData.accounts[phone] = {
      ...currentAccount,
      ...updateData,
      updated_at: new Date().toISOString()
    };

    // 特别处理 stats 对象的合并更新
    if (updateData.stats && currentAccount.stats) {
      platformData.accounts[phone].stats = {
        ...currentAccount.stats,  // 保留现有的 stats 字段
        ...updateData.stats       // 只更新传入的 stats 字段
      };
    }

    platformData.metadata.last_batch_update = new Date().toISOString();

    // 记录更新后的数据
    console.log(`📝 更新账号 ${phone} 数据后:`);
    console.log(`  新数据:`, JSON.stringify(platformData.accounts[phone], null, 2));

    // 保存到数据库
    console.log(`💾 开始保存账号 ${phone} 数据到数据库...`);
    console.log(`  主账号ID: ${mainAccountId}`);
    console.log(`  数据大小: ${JSON.stringify(platformData).length} 字符`);

    const updateResult = await db.prepare(`
      UPDATE main_account_platform_data
      SET platform_accounts_data = ?, updated_at = ?
      WHERE main_account_id = ?
    `).bind(JSON.stringify(platformData), new Date().toISOString(), mainAccountId).run();

    console.log(`💾 数据库更新结果:`, updateResult);
    console.log(`  影响行数: ${(updateResult as any).changes || 'unknown'}`);
    console.log(`  是否成功: ${updateResult.success}`);

    // 检查更新是否成功
    if (!updateResult.success) {
      console.error(`❌ 数据库更新失败 (mainAccountId: ${mainAccountId}):`, updateResult.error);
      return { success: false, message: `数据库更新失败: ${updateResult.error || '未知错误'}` };
    }

    // 检查是否有行被更新（如果 changes 属性存在）
    const changes = (updateResult as any).changes;
    if (changes !== undefined && changes === 0) {
      console.error(`❌ 数据库更新失败: 没有行被更新 (mainAccountId: ${mainAccountId})`);
      return { success: false, message: "数据库更新失败: 没有找到对应的记录" };
    }

    console.log(`✅ 账号 ${phone} 数据库更新成功`);

    // 验证数据是否真的被写入
    try {
      const verifyResult = await db.prepare(`
        SELECT platform_accounts_data FROM main_account_platform_data WHERE main_account_id = ?
      `).bind(mainAccountId).first<{ platform_accounts_data: string }>();

      if (verifyResult) {
        const verifyData = JSON.parse(verifyResult.platform_accounts_data);
        const verifyAccount = verifyData.accounts[phone];
        console.log(`🔍 验证写入结果 - 账号 ${phone}:`);
        console.log(`  总收益: ${verifyAccount?.stats?.total_income || 'N/A'}`);
        console.log(`  昨日收益: ${verifyAccount?.stats?.yesterday_income || 'N/A'}`);
        console.log(`  可提现: ${verifyAccount?.stats?.can_withdraw_amount || 'N/A'}`);
        console.log(`  更新时间: ${verifyAccount?.updated_at || 'N/A'}`);
      } else {
        console.error(`❌ 验证失败: 无法读取更新后的数据`);
      }
    } catch (verifyError) {
      console.error(`❌ 验证数据写入时出错:`, verifyError);
    }

    return {
      success: true,
      message: "平台账号更新成功"
    };
  } catch (error) {
    console.error("更新平台账号失败:", error);
    return { success: false, message: `更新失败: ${error}` };
  }
}

// 清零所有账号的昨日阅读和昨日收益（定时任务用）
export async function resetYesterdayData(db: D1Database): Promise<{ success: boolean; message: string; affectedAccounts: number }> {
  try {
    console.log('=== 开始执行昨日数据重置任务 ===');

    // 获取所有主账号的平台数据
    console.log('开始查询所有主账号的平台数据...');
    const allMainAccountsData = await db.prepare(`
      SELECT main_account_id, platform_accounts_data FROM main_account_platform_data
    `).all<{ main_account_id: number; platform_accounts_data: string }>();

    console.log(`查询结果: ${allMainAccountsData.results?.length || 0} 个主账号`);
    if (!allMainAccountsData.results || allMainAccountsData.results.length === 0) {
      console.log('没有找到任何主账号数据，重置任务结束');
      return { success: true, message: "没有找到任何平台账号数据", affectedAccounts: 0 };
    }

    let totalAffectedAccounts = 0;
    const now = new Date().toISOString();

    // 遍历每个主账号的数据
    for (const mainAccountData of allMainAccountsData.results) {
      try {
        console.log(`处理主账号 ${mainAccountData.main_account_id}...`);
        console.log(`平台数据长度: ${mainAccountData.platform_accounts_data?.length || 0}`);
        console.log(`平台数据前100字符: ${mainAccountData.platform_accounts_data?.substring(0, 100) || 'null'}`);

        // 解析JSON数据
        const platformData = validatePlatformAccountsCollection(mainAccountData.platform_accounts_data);
        if (!platformData) {
          console.log(`主账号 ${mainAccountData.main_account_id} 的平台数据验证失败，跳过`);
          console.log(`失败的数据内容: ${mainAccountData.platform_accounts_data}`);
          continue;
        }

        const accountsCount = Object.keys(platformData.accounts).length;
        console.log(`主账号 ${mainAccountData.main_account_id} 下有 ${accountsCount} 个平台账号`);

        let accountsUpdated = false;

        // 遍历该主账号下的所有平台账号
        for (const [phone, accountData] of Object.entries(platformData.accounts)) {
          // 确保stats对象存在
          if (!accountData.stats) {
            accountData.stats = {
              followers: '0',
              total_reads: '0',
              total_income: '0',
              yesterday_reads: '0',
              yesterday_income: '0',
              credit_score: '0',
              can_withdraw_amount: '0'
            };
          }

          // 无条件重置所有昨日数据
          accountData.stats.yesterday_reads = '0';
          accountData.stats.yesterday_income = '0';
          (accountData as any).is_yesterday_income_ready = false;
          accountData.updated_at = now;
          accountsUpdated = true;
          totalAffectedAccounts++;
          console.log(`清零账号 ${phone} 的昨日数据`);
        }

        // 如果有账号数据被更新，则保存到数据库
        if (accountsUpdated) {
          platformData.metadata.last_batch_update = now;

          await db.prepare(`
            UPDATE main_account_platform_data
            SET platform_accounts_data = ?, updated_at = ?
            WHERE main_account_id = ?
          `).bind(JSON.stringify(platformData), now, mainAccountData.main_account_id).run();
        }
      } catch (error) {
        // 继续处理下一个主账号，不要因为单个账号出错而中断整个任务
        continue;
      }
    }

    const message = `昨日数据清零任务完成，共处理 ${totalAffectedAccounts} 个平台账号`;
    console.log(message);

    return {
      success: true,
      message,
      affectedAccounts: totalAffectedAccounts
    };
  } catch (error) {
    const errorMessage = `昨日数据清零任务失败: ${error}`;
    console.error(errorMessage);
    return {
      success: false,
      message: errorMessage,
      affectedAccounts: 0
    };
  }
}

// 删除平台账号
export async function deletePlatformAccount(
  db: D1Database,
  userId: number,
  phone: string
): Promise<{ success: boolean; message: string }> {
  try {
    const mainAccountId = await getMainAccountId(db, userId);

    // 获取现有数据
    const result = await db.prepare(`
      SELECT platform_accounts_data FROM main_account_platform_data WHERE main_account_id = ?
    `).bind(mainAccountId).first<{ platform_accounts_data: string }>();

    if (!result) {
      return { success: false, message: "未找到平台账号数据" };
    }

    // 使用验证函数解析JSON数据
    const platformData = validatePlatformAccountsCollection(result.platform_accounts_data);
    if (!platformData) {
      return { success: false, message: "数据格式错误或无效" };
    }

    // 检查账号是否存在
    if (!platformData.accounts[phone]) {
      return { success: false, message: "平台账号不存在" };
    }

    // 检查权限
    const userInfo = await db.prepare(`
      SELECT account_type FROM users WHERE id = ?
    `).bind(userId).first<{ account_type: string }>();

    const isMainAccount = userInfo?.account_type === '主账号';
    const currentAccount = platformData.accounts[phone];

    if (!isMainAccount && currentAccount.owner_id !== userId) {
      return { success: false, message: "没有权限删除此平台账号" };
    }

    // 删除账号
    delete platformData.accounts[phone];
    platformData.metadata.total_accounts = Object.keys(platformData.accounts).length;
    platformData.metadata.last_batch_update = new Date().toISOString();

    // 保存到数据库
    await db.prepare(`
      UPDATE main_account_platform_data
      SET platform_accounts_data = ?, updated_at = ?
      WHERE main_account_id = ?
    `).bind(JSON.stringify(platformData), new Date().toISOString(), mainAccountId).run();

    return {
      success: true,
      message: "平台账号删除成功"
    };
  } catch (error) {
    console.error("删除平台账号失败:", error);
    return { success: false, message: `删除失败: ${error}` };
  }
}

// 转移平台账号归属（只有主账号可以操作）
export async function transferPlatformAccount(
  db: D1Database,
  userId: number,
  phone: string,
  newHolderId: number
): Promise<{ success: boolean; message: string }> {
  try {
    const mainAccountId = await getMainAccountId(db, userId);

    // 验证操作者是主账号
    const userInfo = await db.prepare(`
      SELECT account_type FROM users WHERE id = ?
    `).bind(userId).first<{ account_type: string }>();

    if (!userInfo || userInfo.account_type !== '主账号') {
      return { success: false, message: "只有主账号可以转移平台账号" };
    }

    // 验证目标账号存在且归属于该主账号
    const targetAccount = await db.prepare(`
      SELECT id, account_type, account_owner
      FROM users
      WHERE id = ? AND (id = ? OR account_owner = ?)
    `).bind(newHolderId, mainAccountId, mainAccountId).first<{ id: number; account_type: string; account_owner: number | null }>();

    if (!targetAccount) {
      return { success: false, message: "目标账号不存在或不归属于当前主账号" };
    }

    // 获取现有数据
    const result = await db.prepare(`
      SELECT platform_accounts_data FROM main_account_platform_data WHERE main_account_id = ?
    `).bind(mainAccountId).first<{ platform_accounts_data: string }>();

    if (!result) {
      return { success: false, message: "未找到平台账号数据" };
    }

    // 使用验证函数解析JSON数据
    const platformData = validatePlatformAccountsCollection(result.platform_accounts_data);
    if (!platformData) {
      return { success: false, message: "数据格式错误或无效" };
    }

    // 检查账号是否存在
    if (!platformData.accounts[phone]) {
      return { success: false, message: "平台账号不存在" };
    }

    // 更新归属
    platformData.accounts[phone].current_holder_id = newHolderId;
    platformData.accounts[phone].updated_at = new Date().toISOString();
    platformData.metadata.last_batch_update = new Date().toISOString();

    // 保存到数据库
    await db.prepare(`
      UPDATE main_account_platform_data
      SET platform_accounts_data = ?, updated_at = ?
      WHERE main_account_id = ?
    `).bind(JSON.stringify(platformData), new Date().toISOString(), mainAccountId).run();

    return {
      success: true,
      message: "平台账号转移成功"
    };
  } catch (error) {
    console.error("转移平台账号失败:", error);
    return { success: false, message: `转移失败: ${error}` };
  }
}

// 批量转移平台账号（解决并发问题）
export async function batchTransferPlatformAccounts(
  db: D1Database,
  userId: number,
  transfers: Array<{ phone: string; newHolderId: number }>
): Promise<{ success: boolean; message: string; results: Array<{ phone: string; success: boolean; message: string }> }> {
  try {
    const mainAccountId = await getMainAccountId(db, userId);

    // 验证操作者是主账号
    const userInfo = await db.prepare(`
      SELECT account_type FROM users WHERE id = ?
    `).bind(userId).first<{ account_type: string }>();

    if (!userInfo || userInfo.account_type !== '主账号') {
      return {
        success: false,
        message: "只有主账号可以转移平台账号",
        results: transfers.map(t => ({ phone: t.phone, success: false, message: "权限不足" }))
      };
    }

    // 获取现有数据
    const result = await db.prepare(`
      SELECT platform_accounts_data FROM main_account_platform_data WHERE main_account_id = ?
    `).bind(mainAccountId).first<{ platform_accounts_data: string }>();

    if (!result) {
      return {
        success: false,
        message: "未找到平台账号数据",
        results: transfers.map(t => ({ phone: t.phone, success: false, message: "未找到平台账号数据" }))
      };
    }

    // 使用验证函数解析JSON数据
    const platformData = validatePlatformAccountsCollection(result.platform_accounts_data);
    if (!platformData) {
      return {
        success: false,
        message: "数据格式错误或无效",
        results: transfers.map(t => ({ phone: t.phone, success: false, message: "数据格式错误" }))
      };
    }

    const results: Array<{ phone: string; success: boolean; message: string }> = [];
    let successCount = 0;

    // 验证所有目标账号
    const targetAccountIds = [...new Set(transfers.map(t => t.newHolderId))];
    const targetAccounts = new Map<number, boolean>();

    for (const targetId of targetAccountIds) {
      const targetAccount = await db.prepare(`
        SELECT id FROM users
        WHERE id = ? AND (id = ? OR account_owner = ?)
      `).bind(targetId, mainAccountId, mainAccountId).first<{ id: number }>();

      targetAccounts.set(targetId, !!targetAccount);
    }

    // 批量处理转移
    for (const transfer of transfers) {
      const { phone, newHolderId } = transfer;

      // 检查目标账号是否有效
      if (!targetAccounts.get(newHolderId)) {
        results.push({
          phone,
          success: false,
          message: "目标账号不存在或不归属于当前主账号"
        });
        continue;
      }

      // 检查平台账号是否存在
      if (!platformData.accounts[phone]) {
        results.push({
          phone,
          success: false,
          message: "平台账号不存在"
        });
        continue;
      }

      // 执行转移
      platformData.accounts[phone].current_holder_id = newHolderId;
      platformData.accounts[phone].updated_at = new Date().toISOString();

      results.push({
        phone,
        success: true,
        message: "转移成功"
      });
      successCount++;
    }

    // 更新元数据
    platformData.metadata.last_batch_update = new Date().toISOString();

    // 一次性保存所有更改
    await db.prepare(`
      UPDATE main_account_platform_data
      SET platform_accounts_data = ?, updated_at = ?
      WHERE main_account_id = ?
    `).bind(JSON.stringify(platformData), new Date().toISOString(), mainAccountId).run();

    return {
      success: true,
      message: `批量转移完成：成功 ${successCount} 个，失败 ${transfers.length - successCount} 个`,
      results
    };
  } catch (error) {
    console.error("批量转移平台账号失败:", error);
    return {
      success: false,
      message: `批量转移失败: ${error}`,
      results: transfers.map(t => ({ phone: t.phone, success: false, message: `系统错误: ${error}` }))
    };
  }
}



// 删除子账号
export async function deleteSubAccount(db: D1Database, ownerId: number, subAccountId: number): Promise<{ success: boolean; message: string }> {
  try {
    // 验证主账号是否存在且为主账号类型
    const owner = await db.prepare(`SELECT id, account_type FROM users WHERE id = ?`).bind(ownerId).first<{ id: number; account_type: string }>();
    if (!owner) {
      return { success: false, message: '主账号不存在' };
    }

    if (owner.account_type !== '主账号') {
      return { success: false, message: '只有主账号才能删除子账号' };
    }

    // 验证子账号是否存在且归属于该主账号
    const subAccount = await db.prepare(`SELECT id, account_owner FROM users WHERE id = ?`).bind(subAccountId).first<{ id: number; account_owner: number | null }>();
    if (!subAccount) {
      return { success: false, message: '子账号不存在' };
    }

    if (subAccount.account_owner !== ownerId) {
      return { success: false, message: '无权限删除此子账号' };
    }

    // 删除子账号
    await db.prepare(`DELETE FROM users WHERE id = ?`).bind(subAccountId).run();

    return {
      success: true,
      message: '子账号删除成功'
    };
  } catch (error) {
    console.error("删除子账号失败:", error);
    return { success: false, message: `删除子账号失败: ${error}` };
  }
}

// 获取子账号列表
export async function getSubAccounts(db: D1Database, ownerId: number): Promise<{ success: boolean; subAccounts?: User[]; message?: string }> {
  try {
    const result = await db.prepare(`
      SELECT * FROM users
      WHERE account_owner = ?
      ORDER BY created_at DESC
    `).bind(ownerId).all<User>();

    return {
      success: true,
      subAccounts: result.results || []
    };
  } catch (error) {
    console.error("获取子账号列表失败:", error);
    return { success: false, message: `获取子账号列表失败: ${error}` };
  }
}